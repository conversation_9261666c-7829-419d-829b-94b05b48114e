import { motion } from 'framer-motion';
import { Calendar, MapPin, Users, TrendingUp } from 'lucide-react';

const Experience = () => {
  const experiences = [
    {
      title: 'Lead Software Engineer',
      company: 'TechCorp Solutions',
      location: 'San Francisco, CA',
      period: '2023 - Present',
      type: 'Full-time',
      description: 'Leading a team of 5 developers in building scalable web applications and microservices architecture.',
      achievements: [
        'Led the development of a new customer portal that increased user engagement by 45%',
        'Implemented CI/CD pipelines reducing deployment time by 60%',
        'Mentored 3 junior developers, helping them advance to mid-level positions',
        'Architected microservices solution handling 1M+ daily requests'
      ],
      technologies: ['React', 'Node.js', 'AWS', 'Docker', 'PostgreSQL', 'TypeScript']
    },
    {
      title: 'Senior Software Engineer',
      company: 'InnovateTech',
      location: 'Austin, TX',
      period: '2022 - 2023',
      type: 'Full-time',
      description: 'Developed and maintained high-performance web applications serving millions of users.',
      achievements: [
        'Optimized application performance resulting in 40% faster load times',
        'Built real-time analytics dashboard using React and WebSocket',
        'Collaborated with product team to deliver 8 major feature releases',
        'Established code review standards improving code quality by 35%'
      ],
      technologies: ['React', 'Python', 'MongoDB', 'Redis', 'GraphQL', 'Jest']
    },
    {
      title: 'Software Engineer',
      company: 'StartupXYZ',
      location: 'Remote',
      period: '2021 - 2022',
      type: 'Full-time',
      description: 'Full-stack development in a fast-paced startup environment, wearing multiple hats.',
      achievements: [
        'Built MVP from scratch using React and Node.js in 3 months',
        'Implemented user authentication and authorization system',
        'Developed RESTful APIs serving mobile and web applications',
        'Participated in product planning and technical decision making'
      ],
      technologies: ['React', 'Node.js', 'Express.js', 'MySQL', 'JavaScript', 'AWS']
    }
  ];

  return (
    <section id="experience" className="section-padding bg-gray-50 dark:bg-gray-900">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Professional Experience
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            A journey of growth, leadership, and technical excellence across diverse projects and teams
          </p>
        </motion.div>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 top-0 bottom-0 w-0.5 bg-primary-200 dark:bg-primary-800"></div>

          {experiences.map((exp, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className={`relative flex items-center mb-16 ${
                index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
              }`}
            >
              {/* Timeline dot */}
              <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-primary-600 rounded-full border-4 border-white dark:border-gray-900 z-10"></div>

              {/* Content */}
              <div className={`w-full md:w-5/12 ml-16 md:ml-0 ${index % 2 === 0 ? 'md:pr-8' : 'md:pl-8'}`}>
                <div className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                  {/* Header */}
                  <div className="mb-6">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {exp.title}
                    </h3>
                    <div className="text-primary-600 dark:text-primary-400 font-semibold text-lg mb-3">
                      {exp.company}
                    </div>
                    <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center gap-1">
                        <Calendar size={16} />
                        {exp.period}
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin size={16} />
                        {exp.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <Users size={16} />
                        {exp.type}
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    {exp.description}
                  </p>

                  {/* Achievements */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                      <TrendingUp size={18} />
                      Key Achievements
                    </h4>
                    <ul className="space-y-2">
                      {exp.achievements.map((achievement, achIndex) => (
                        <li key={achIndex} className="text-gray-600 dark:text-gray-400 text-sm flex items-start gap-2">
                          <span className="text-primary-600 dark:text-primary-400 mt-1">•</span>
                          {achievement}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Technologies */}
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      Technologies Used
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {exp.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full text-xs font-medium"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Experience;
