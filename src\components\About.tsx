import { motion } from 'framer-motion';
import { Code, Users, Lightbulb, Target } from 'lucide-react';

const About = () => {
  const highlights = [
    {
      icon: <Code size={24} />,
      title: 'Technical Excellence',
      description: 'Proficient in modern web technologies including React, TypeScript, Node.js, and cloud platforms.'
    },
    {
      icon: <Users size={24} />,
      title: 'Team Leadership',
      description: 'Led cross-functional teams of 5+ developers, mentoring junior engineers and driving project success.'
    },
    {
      icon: <Lightbulb size={24} />,
      title: 'Innovation Focus',
      description: 'Passionate about implementing cutting-edge solutions and staying ahead of technology trends.'
    },
    {
      icon: <Target size={24} />,
      title: 'Results Driven',
      description: 'Delivered 15+ successful projects, improving system performance by 40% and user satisfaction by 35%.'
    }
  ];

  return (
    <section id="about" className="section-padding bg-gray-50 dark:bg-gray-900">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            About Me
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            A passionate software engineer with a proven track record of building scalable applications 
            and leading high-performing development teams.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left side - Text content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Lead Software Engineer with 3 Years of Excellence
            </h3>
            <div className="space-y-4 text-gray-600 dark:text-gray-400">
              <p>
                I'm a dedicated software engineer who thrives on solving complex problems and building 
                innovative solutions. Over the past 3 years, I've evolved from a passionate developer 
                to a technical leader, guiding teams through challenging projects and delivering 
                exceptional results.
              </p>
              <p>
                My journey in software engineering has been marked by continuous learning and growth. 
                I specialize in full-stack development with a strong focus on modern web technologies, 
                cloud architecture, and agile methodologies. I believe in writing clean, maintainable 
                code and fostering a collaborative team environment.
              </p>
              <p>
                When I'm not coding, you'll find me exploring new technologies, contributing to open-source 
                projects, or mentoring aspiring developers. I'm passionate about sharing knowledge and 
                helping others grow in their technical careers.
              </p>
            </div>

            <div className="mt-8">
              <button className="btn-primary">
                Download Resume
              </button>
            </div>
          </motion.div>

          {/* Right side - Highlights grid */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid sm:grid-cols-2 gap-6"
          >
            {highlights.map((highlight, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="text-primary-600 dark:text-primary-400 mb-4">
                  {highlight.icon}
                </div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {highlight.title}
                </h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {highlight.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Stats section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            { number: '3+', label: 'Years Experience' },
            { number: '15+', label: 'Projects Completed' },
            { number: '5+', label: 'Team Members Led' },
            { number: '40%', label: 'Performance Improvement' }
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 dark:text-gray-400 text-sm">
                {stat.label}
              </div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default About;
