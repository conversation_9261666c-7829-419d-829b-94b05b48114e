import { motion } from 'framer-motion';
import { ExternalLink, Github, Star, Users } from 'lucide-react';

const Projects = () => {
  const projects = [
    {
      title: 'E-Commerce Platform',
      description: 'A full-stack e-commerce solution with real-time inventory management, payment processing, and admin dashboard. Built for scalability with microservices architecture.',
      image: '/api/placeholder/600/400',
      technologies: ['React', 'Node.js', 'PostgreSQL', 'Redis', 'AWS', 'Stripe'],
      features: [
        'Real-time inventory tracking',
        'Secure payment processing',
        'Admin analytics dashboard',
        'Mobile-responsive design'
      ],
      metrics: {
        users: '10K+',
        performance: '99.9%',
        rating: '4.8/5'
      },
      links: {
        live: 'https://example.com',
        github: 'https://github.com/example'
      },
      status: 'Production'
    },
    {
      title: 'Task Management System',
      description: 'A collaborative project management tool with real-time updates, team collaboration features, and advanced reporting capabilities.',
      image: '/api/placeholder/600/400',
      technologies: ['React', 'TypeScript', 'Express.js', 'MongoDB', 'Socket.io'],
      features: [
        'Real-time collaboration',
        'Advanced task filtering',
        'Team performance analytics',
        'Custom workflow automation'
      ],
      metrics: {
        users: '5K+',
        performance: '99.5%',
        rating: '4.7/5'
      },
      links: {
        live: 'https://example.com',
        github: 'https://github.com/example'
      },
      status: 'Production'
    },
    {
      title: 'AI-Powered Analytics Dashboard',
      description: 'An intelligent analytics platform that provides actionable insights using machine learning algorithms and real-time data processing.',
      image: '/api/placeholder/600/400',
      technologies: ['React', 'Python', 'TensorFlow', 'FastAPI', 'PostgreSQL', 'Docker'],
      features: [
        'Machine learning predictions',
        'Real-time data visualization',
        'Custom report generation',
        'API integration capabilities'
      ],
      metrics: {
        users: '2K+',
        performance: '99.7%',
        rating: '4.9/5'
      },
      links: {
        live: 'https://example.com',
        github: 'https://github.com/example'
      },
      status: 'Beta'
    },
    {
      title: 'Open Source Component Library',
      description: 'A comprehensive React component library with TypeScript support, extensive documentation, and accessibility features.',
      image: '/api/placeholder/600/400',
      technologies: ['React', 'TypeScript', 'Storybook', 'Jest', 'Rollup'],
      features: [
        'Fully accessible components',
        'TypeScript definitions',
        'Comprehensive documentation',
        'Theme customization'
      ],
      metrics: {
        users: '1K+',
        performance: 'N/A',
        rating: '4.6/5'
      },
      links: {
        live: 'https://storybook.example.com',
        github: 'https://github.com/example'
      },
      status: 'Open Source'
    }
  ];

  return (
    <section id="projects" className="section-padding bg-white dark:bg-gray-800">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Featured Projects
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            A showcase of my recent work, demonstrating technical expertise and problem-solving abilities
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="project-card bg-gray-50 dark:bg-gray-900 rounded-xl overflow-hidden shadow-lg"
            >
              {/* Project Image */}
              <div className="relative h-48 bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center">
                <div className="text-white text-6xl font-bold opacity-20">
                  {project.title.split(' ').map(word => word[0]).join('')}
                </div>
                <div className="absolute top-4 right-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    project.status === 'Production' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : project.status === 'Beta'
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                  }`}>
                    {project.status}
                  </span>
                </div>
              </div>

              <div className="p-6">
                {/* Project Header */}
                <div className="mb-4">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {project.description}
                  </p>
                </div>

                {/* Technologies */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-2 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded text-xs font-medium"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Features */}
                <div className="mb-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2 text-sm">
                    Key Features:
                  </h4>
                  <ul className="text-gray-600 dark:text-gray-400 text-xs space-y-1">
                    {project.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-2">
                        <span className="text-primary-600 dark:text-primary-400 mt-1">•</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Metrics */}
                <div className="mb-6">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="flex items-center justify-center gap-1 text-primary-600 dark:text-primary-400 mb-1">
                        <Users size={14} />
                      </div>
                      <div className="text-xs font-semibold text-gray-900 dark:text-white">
                        {project.metrics.users}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Users</div>
                    </div>
                    <div>
                      <div className="flex items-center justify-center gap-1 text-primary-600 dark:text-primary-400 mb-1">
                        <Star size={14} />
                      </div>
                      <div className="text-xs font-semibold text-gray-900 dark:text-white">
                        {project.metrics.rating}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Rating</div>
                    </div>
                    <div>
                      <div className="flex items-center justify-center gap-1 text-primary-600 dark:text-primary-400 mb-1">
                        <ExternalLink size={14} />
                      </div>
                      <div className="text-xs font-semibold text-gray-900 dark:text-white">
                        {project.metrics.performance}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Uptime</div>
                    </div>
                  </div>
                </div>

                {/* Links */}
                <div className="flex gap-3">
                  <a
                    href={project.links.live}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 btn-primary text-center text-sm py-2"
                  >
                    <ExternalLink size={16} />
                    Live Demo
                  </a>
                  <a
                    href={project.links.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 btn-secondary text-center text-sm py-2"
                  >
                    <Github size={16} />
                    Code
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Projects;
